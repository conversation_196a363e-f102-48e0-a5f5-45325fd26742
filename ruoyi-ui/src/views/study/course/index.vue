<template>
  <div class="app-container">
    <!-- 面包屑导航 -->
    <div class="mb8" v-if="className">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>学时管理</el-breadcrumb-item>
        <el-breadcrumb-item>班次管理</el-breadcrumb-item>
        <el-breadcrumb-item>{{ className }} - 课堂管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item :label="'当前课程: ' + this.className" label-width="300px" prop="courseName"/>-->
      <el-form-item class="current-course-item">
        <span class="course-label" size="small">当前课程: </span>
        <span class="course-name" size="small">{{ this.className }}</span>
      </el-form-item>
      <br>
      <el-form-item label="课堂名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课堂名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
<!--        <el-button icon="el-icon-back" size="mini" @click="handleClose">返回</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-back"
          size="mini"
          @click="handleClose"
        >返回班次管理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['study:course:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['study:course:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['study:course:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['study:course:export']"
        >导出</el-button>
      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课堂ID" align="center" prop="courseId" />
      <el-table-column label="课堂名称" align="center" prop="courseName" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学时" align="center" prop="hours" />
      <el-table-column label="学分" align="center" prop="credit" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['study:course:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['study:course:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改课堂信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="课堂名称" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课堂名称" />
        </el-form-item>
        <el-form-item label="教师姓名" prop="teacherName">
          <el-input v-model="form.teacherName" placeholder="请输入教师姓名" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable size="small"
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择课堂开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable size="small"
            v-model="form.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择课堂结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学时" prop="hours">
          <el-input-number v-model="form.hours" placeholder="请输入学时" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="学分" prop="credit">
          <el-input-number v-model="form.credit" placeholder="请输入学分" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<!-- 当前课程的样式调整 -->
<style scoped>
.current-course-item {
  margin-right: 10px;
  display: inline-flex;
  align-items: center;
}
.course-label {
  font-size: 14px; /* Match default el-form-item label size */
  color: #606266; /* Match default label color */
  margin-right: 5px;
}
.course-name {
  font-size: 14px; /* Same as input text size */
  font-weight: normal;
}
</style>
<script>
import { listCourse, getCourse, delCourse, addCourse, updateCourse } from "@/api/study/course";

export default {
  name: "Course",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课堂信息表格数据
      courseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 班次ID
      classId: undefined,
      // 班次名称
      className: undefined,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: null,
        courseName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        courseName: [
          { required: true, message: "课堂名称不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "课堂开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "课堂结束时间不能为空", trigger: "blur" }
        ],
        hours: [
          { required: true, message: "学时不能为空", trigger: "blur" }
        ],
        credit: [
          { required: true, message: "学分不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.initializeData();
  },
  watch: {
    // 监听路由变化，重新初始化数据
    '$route'(to, from) {
      // 只有当路由查询参数中的classId变化时才重新初始化
      if (to.query.classId !== from.query.classId) {
        this.initializeData();
      }
    }
  },
  methods: {
    /** 初始化页面数据 */
    initializeData() {
      const classId = this.$route.query && this.$route.query.classId;
      const className = this.$route.query && this.$route.query.className;
      if (classId) {
        this.classId = classId;
        this.queryParams.classId = classId;
        // 使用 decodeURIComponent 对中文参数进行解码
        try {
          this.className = className ? decodeURIComponent(className) : '未知班次';
        } catch (e) {
          this.className = className || '未知班次';
        }
        // 重置分页和搜索条件
        this.queryParams.pageNum = 1;
        this.queryParams.courseName = null;
        this.getList();
      } else {
        this.$modal.msgError("缺少班次ID，无法查询课堂信息。请从班次管理页面进入课堂管理。");
        // 延迟跳转，给用户时间看到错误信息
        setTimeout(() => {
          this.$router.push('/study/class');
        }, 2000);
      }
    },
    /** 查询课堂信息列表 */
    getList() {
      this.loading = true;
      listCourse(this.queryParams).then(response => {
        this.courseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        courseId: null,
        classId: this.classId,
        courseName: null,
        teacherName: null,
        startTime: null,
        endTime: null,
        hours: null,
        credit: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // queryParams中的classId不能重置
      this.queryParams.courseName = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.courseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课堂信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const courseId = row.courseId || this.ids
      getCourse(courseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改课堂信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          //校验时间信息
          const classStartDate = this.$route.query.classStartDate;
          const classEndDate = this.$route.query.classEndDate;
          //课时时间
          const startTime = formatDate(this.form.startTime);
          const endTime = formatDate(this.form.endTime);
          //校验 课程开始时间<课程结束时间
          if(startTime > endTime){
            this.$modal.msg("课程开始时间不能滞后于课程结束时间");
          }else if(endTime > classEndDate){
            //校验 课程结束时间>班次结束时间
            this.$modal.msg("课程结束时间不能滞后于班次结束时间");
          }else if(startTime < classStartDate){
            //校验 课程开始时间>班次开始时间
            this.$modal.msg("课程开始时间不能提前于班次开始时间");
          }else{
            if (this.form.courseId != null) {
              updateCourse(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              //进行新增
              addCourse(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const courseIds = row.courseId || this.ids;
      this.$modal.confirm('是否确认删除课堂信息编号为"' + courseIds + '"的数据项？').then(function() {
        return delCourse(courseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('study/course/export', {
        ...this.queryParams
      }, `course_${new Date().getTime()}.xlsx`)
    },
    /** 返回班次管理 */
    handleClose() {
      // 安全地跳转到班次管理页面，清理当前路由状态
      this.$router.replace({ path: '/study/class' });
    }
  }
};

//转换日期，变换为yyyy-mm-dd
function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
</script>

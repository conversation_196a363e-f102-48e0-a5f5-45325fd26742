<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff4d4f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d4380d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#d4380d" stroke-width="2"/>

  <!-- 党徽五角星 -->
  <polygon points="32,8 34.5,15 42,15 36.5,19.5 39,26.5 32,22 25,26.5 27.5,19.5 22,15 29.5,15" fill="#ffd700" stroke="#ffec3d" stroke-width="0.5"/>

  <!-- 书本图标 -->
  <rect x="16" y="28" width="32" height="22" rx="2" fill="url(#bookGradient)" stroke="#d9d9d9" stroke-width="1"/>
  <rect x="18" y="30" width="28" height="18" rx="1" fill="#ffffff"/>

  <!-- 书页线条 -->
  <line x1="20" y1="34" x2="44" y2="34" stroke="#1890ff" stroke-width="1.5"/>
  <line x1="20" y1="38" x2="40" y2="38" stroke="#1890ff" stroke-width="1"/>
  <line x1="20" y1="42" x2="42" y2="42" stroke="#1890ff" stroke-width="1"/>
  <line x1="20" y1="46" x2="38" y2="46" stroke="#1890ff" stroke-width="1"/>

  <!-- 党字 -->
  <text x="32" y="58" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="SimHei, 黑体">党</text>
</svg>

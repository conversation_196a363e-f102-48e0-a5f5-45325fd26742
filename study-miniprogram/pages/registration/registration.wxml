<view class="container">
  <!-- 标签页 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{activeTab === 0 ? 'active' : ''}}"
      data-index="0"
      bindtap="switchTab"
    >
      可报名班次
    </view>
    <view 
      class="tab-item {{activeTab === 1 ? 'active' : ''}}"
      data-index="1"
      bindtap="switchTab"
    >
      我的报名
    </view>
  </view>

  <!-- 可报名班次 -->
  <view wx:if="{{activeTab === 0}}" class="tab-content">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{classList.length === 0}}" class="empty">
      <text>暂无可报名班次</text>
    </view>
    
    <view wx:else class="class-list">
      <view 
        wx:for="{{classList}}" 
        wx:key="classId" 
        class="class-item"
        bindtap="viewClassDetail"
        data-class-item="{{item}}"
      >
        <view class="class-header">
          <text class="class-name">{{item.className}}</text>
          <text class="status-tag {{item.canRegister ? 'status-success' : 'status-danger'}}">
            {{item.statusText}}
          </text>
        </view>
        
        <view class="class-info">
          <view class="info-row">
            <text class="label">报名时间：</text>
            <text class="value">{{item.enrollStartDate}} ~ {{item.enrollEndDate}}</text>
          </view>
          <view class="info-row">
            <text class="label">培训时间：</text>
            <text class="value">{{item.classStartDate}} ~ {{item.classEndDate}}</text>
          </view>
          <view class="info-row" wx:if="{{item.requiredHours}}">
            <text class="label">总学时：</text>
            <text class="value">{{item.requiredHours}}小时</text>
          </view>
        </view>
        
        <view class="class-actions">
          <button 
            wx:if="{{item.canRegister}}"
            class="register-btn"
            size="mini"
            type="primary"
            bindtap="handleRegister"
            data-class-item="{{item}}"
            catchtap="true"
          >
            立即报名
          </button>
          <button 
            wx:else
            class="register-btn disabled"
            size="mini"
            disabled
          >
            {{item.statusText}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 我的报名 -->
  <view wx:if="{{activeTab === 1}}" class="tab-content">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{myRegistrations.length === 0}}" class="empty">
      <text>暂无报名记录</text>
    </view>
    
    <view wx:else class="registration-list">
      <view 
        wx:for="{{myRegistrations}}" 
        wx:key="registrationId" 
        class="registration-item">
        <view class="registration-header">
          <text class="class-name">{{item.className}}</text>
          <text class="status-tag {{item.statusClass}}">
            {{item.statusText}}
          </text>
        </view>
        <view class="registration-info">
          <view class="info-row">
            <text class="label">报名时间：</text>
            <text class="value">{{item.registrationTime || item.createTime}}</text>
          </view>
          <view class="info-row">
            <text class="label">培训时间：</text>
            <text class="value">{{item.displayStartDate}} ~ {{item.displayEndDate}}</text>
          </view>
          <view class="info-row" wx:if="{{item.achievedHours}}">
            <text class="label">已获学时：</text>
            <text class="value">{{item.achievedHours}}小时</text>
          </view>
          <view class="info-row" wx:if="{{item.remark}}">
            <text class="label">备注：</text>
            <text class="value">{{item.remark}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 
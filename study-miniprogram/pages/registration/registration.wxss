.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  transition: all 0.3s;
}

.tab-item.active {
  color: #409EFF;
  background-color: #ECF5FF;
  font-weight: bold;
}

/* 内容区域 */
.tab-content {
  min-height: 600rpx;
}

.loading, .empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 班次列表 */
.class-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.class-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.class-header, .registration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 70rpx;
  margin-bottom: 16rpx;
}

.class-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.class-info {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  align-items: center;
  min-height: 40rpx;
}

.info-row .label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-row .value {
  color: #333;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

.class-actions {
  text-align: right;
}

.status-tag {
  padding: 10rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: white;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  box-sizing: border-box;
  white-space: nowrap;
  margin-left: 8rpx;
  min-height: 48rpx;
  min-width: 80rpx;
}

.status-success { background-color: #52c41a; }
.status-warning { background-color: #faad14; }
.status-danger  { background-color: #ff4d4f; }
.status-info    { background-color: #1890ff; }

.register-btn {
  height: 48rpx;
  line-height: 48rpx;
  font-size: 24rpx;
  border-radius: 24rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0 28rpx;
  box-sizing: border-box;
  font-weight: 500;
}

.register-btn.disabled {
  background-color: #ccc !important;
  color: #999 !important;
}

/* 报名列表 */
.registration-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.registration-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.registration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  min-height: 60rpx;
}

.registration-info {
  color: #666;
} 
.container {
  padding: 24rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 24rpx;
}
.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}
.class-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  box-sizing: border-box;
  white-space: nowrap;
  margin-left: 12rpx;
}
.status-success { background-color: #52c41a; }
.status-warning { background-color: #faad14; }
.status-danger  { background-color: #ff4d4f; }
.status-info    { background-color: #1890ff; }
.sub-info {
  font-size: 26rpx;
  color: #666;
}

.info-card {
  margin-bottom: 24rpx;
}
.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}
.label {
  color: #888;
  width: 160rpx;
  flex-shrink: 0;
}
.value {
  color: #222;
  flex: 1;
  word-break: break-all;
  white-space: pre-line;
}

.footer-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 24rpx 0 32rpx 0;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  justify-content: center;
}

.btn-primary {
  width: 80vw;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
} 
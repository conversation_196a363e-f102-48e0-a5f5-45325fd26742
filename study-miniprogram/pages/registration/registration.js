const app = getApp()

Page({
  data: {
    classList: [],
    myRegistrations: [],
    loading: false,
    activeTab: 0, // 0: 可报名班次, 1: 我的报名
    userInfo: {},
    hasLoaded: false // 标记是否已经加载过
  },

  onLoad() {
    this.loadUserInfo()
    // 初始化时只加载第一个标签页的数据
    this.loadAvailableClasses()
    this.setData({ hasLoaded: true })
  },

  onShow() {
    // 只有在已经加载过的情况下才刷新数据
    if (this.data.hasLoaded) {
      if (this.data.activeTab === 0) {
        this.loadAvailableClasses()
      } else {
        this.loadMyRegistrations()
      }
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    } else {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
    }
  },

  // 切换标签页
  switchTab(e) {
    const dataset = e.currentTarget.dataset
    const index = parseInt(dataset.index)
    const currentTab = this.data.activeTab
    
    // 如果点击的是当前标签页，直接返回
    if (currentTab === index) {
      return
    }
    
    // 先更新标签页状态
    this.setData({ 
      activeTab: index,
      loading: false
    })
    
    // 根据标签页加载对应数据
    if (index === 0) {
      this.loadAvailableClasses()
    } else if (index === 1) {
      this.loadMyRegistrations()
    }
  },

  // 加载可报名班次
  async loadAvailableClasses() {
    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: '/study/class/list',
        data: {
          pageNum: 1,
          pageSize: 50
        }
      })

      if (res && res.rows) {
        const classList = res.rows.map(item => ({
          ...item,
          canRegister: this.canRegister(item),
          statusText: this.getClassStatusText(item)
        }))
        
        this.setData({ classList })
      } else {
        this.setData({ classList: [] })
      }
    } catch (error) {
      console.error('加载班次列表失败:', error)
      wx.showToast({
        title: '加载班次失败',
        icon: 'none'
      })
      this.setData({ classList: [] })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载我的报名
  async loadMyRegistrations() {
    const userInfo = this.data.userInfo
    if (!userInfo || !userInfo.userId) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: '/study/registration/list',
        data: {
          pageNum: 1,
          pageSize: 50,
          userId: userInfo.userId
        }
      })

      if (res && res.rows) {
        // 为每个报名记录获取完整的班级信息
        const myRegistrations = await Promise.all(res.rows.map(async (item) => {
          try {
            // 获取完整的班级信息
            const classRes = await app.request({
              url: '/study/class/list',
              data: {
                classId: item.classId
              }
            })

            let fullClassInfo = null
            if (classRes && classRes.rows && classRes.rows.length > 0) {
              fullClassInfo = classRes.rows[0]
            }

            return {
              ...item,
              statusText: this.getRegistrationStatusText(item.status),
              statusClass: this.getRegistrationStatusClass(item.status),
              className: item.studyClass?.className || fullClassInfo?.className || '未知班次',
              // 补充完整的班级信息
              fullClassInfo: fullClassInfo,
              displayStartDate: fullClassInfo?.classStartDate || '待定',
              displayEndDate: fullClassInfo?.classEndDate || '待定'
            }
          } catch (error) {
            console.error('获取班级信息失败:', error)
            return {
              ...item,
              statusText: this.getRegistrationStatusText(item.status),
              statusClass: this.getRegistrationStatusClass(item.status),
              className: item.studyClass?.className || '未知班次',
              displayStartDate: '获取失败',
              displayEndDate: '获取失败'
            }
          }
        }))
        
        this.setData({ myRegistrations })
      } else {
        this.setData({ myRegistrations: [] })
      }
    } catch (error) {
      console.error('加载我的报名失败:', error)
      wx.showToast({
        title: '加载报名记录失败',
        icon: 'none'
      })
      this.setData({ myRegistrations: [] })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 判断是否可以报名
  canRegister(classItem) {
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    
    return now >= enrollStart && now <= enrollEnd
  },

  // 获取班次状态文字
  getClassStatusText(classItem) {
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    const classStart = new Date(classItem.classStartDate)
    
    if (now < enrollStart) {
      return '报名未开始'
    } else if (now > enrollEnd) {
      return '报名已结束'
    } else if (now > classStart) {
      return '培训中'
    } else {
      return '可报名'
    }
  },

  // 获取报名状态文字
  getRegistrationStatusText(status) {
    const statusMap = {
      '0': '待审核',
      '1': '已通过',
      '2': '已拒绝'
    }
    return statusMap[status] || '未知'
  },

  // 获取报名状态样式类
  getRegistrationStatusClass(status) {
    const classMap = {
      '0': 'status-pending',
      '1': 'status-approved',
      '2': 'status-rejected'
    }
    return classMap[status] || ''
  },

  // 立即报名
  async handleRegister(e) {
    const { classItem } = e.currentTarget.dataset
    if (!classItem || !classItem.classId) {
      wx.showToast({ title: '班级信息异常', icon: 'none' })
      return
    }
    const userInfo = this.data.userInfo
    if (!userInfo || !userInfo.userId) {
      wx.showToast({ title: '请先登录', icon: 'none' })
      return
    }
    // 参数类型强校验
    const classId = Number(classItem.classId)
    const userId = Number(userInfo.userId)
    if (isNaN(classId) || isNaN(userId)) {
      wx.showToast({ title: '参数类型错误', icon: 'none' })
      return
    }
    wx.showLoading({ title: '报名中...' })
    try {
      await app.request({
        url: '/study/registration/add',
        method: 'POST',
        data: {
          classId,
          userId,
          status: '0' // 待审核
        }
      })
      wx.showToast({ title: '报名成功', icon: 'success' })
      this.loadAvailableClasses()
      this.loadMyRegistrations()
    } catch (e) {
      wx.showToast({ title: (e && e.msg) || '报名失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 查看班次详情
  viewClassDetail(e) {
    const { classItem } = e.currentTarget.dataset
    
    let contentLines = []
    
    // 时间信息
    contentLines.push(`📅 报名开始时间`)
    contentLines.push(`${classItem.enrollStartDate}`)
    contentLines.push('')
    
    contentLines.push(`⏰ 报名结束时间`)
    contentLines.push(`${classItem.enrollEndDate}`)
    contentLines.push('')
    
    contentLines.push(`🎓 培训开始时间`)
    contentLines.push(`${classItem.classStartDate}`)
    contentLines.push('')
    
    contentLines.push(`🏁 培训结束时间`)
    contentLines.push(`${classItem.classEndDate}`)
    contentLines.push('')
    
    // 班次要求
    if (classItem.requiredHours) {
      contentLines.push(`⭐ 总学时`)
      contentLines.push(`${classItem.requiredHours}小时`)
      contentLines.push('')
    }
    if (classItem.credits) {
      contentLines.push(`🏆 总学分`)
      contentLines.push(`${classItem.credits}分`)
      contentLines.push('')
    }
    
    // 班次描述
    if (classItem.description) {
      contentLines.push(`📄 班次描述`)
      contentLines.push(`${classItem.description}`)
    }
    
    // 移除最后的空行
    while (contentLines.length > 0 && contentLines[contentLines.length - 1] === '') {
      contentLines.pop()
    }
    
    const content = contentLines.join('\r\n')
    
    wx.showModal({
      title: `📚 ${classItem.className}`,
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 查看报名详情
  viewRegistrationDetail(e) {
    const { registration } = e.currentTarget.dataset
    console.log('registration:', registration)
    if (!registration || !registration.registrationId || isNaN(Number(registration.registrationId))) {
      wx.showToast({
        title: '报名ID异常',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: `/pages/registration/detail?registrationId=${registration.registrationId}`
    })
  }
}) 
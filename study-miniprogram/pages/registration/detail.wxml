<view class="container">
  <view class="header-card card">
    <view class="header-main">
      <text class="class-name">{{detail.className}}</text>
      <text class="status-tag {{detail.statusClass}}">{{detail.statusText}}</text>
    </view>
    <view class="sub-info">
      <text class="label">报名时间：</text>
      <text class="value">{{detail.registrationTime || detail.createTime}}</text>
    </view>
  </view>

  <view class="info-card card">
    <view class="info-row">
      <text class="label">培训时间：</text>
      <text class="value">{{detail.displayStartDate}} ~ {{detail.displayEndDate}}</text>
    </view>
    <view class="info-row">
      <text class="label">总学时：</text>
      <text class="value">{{detail.requiredHours}} 小时</text>
    </view>
    <view class="info-row">
      <text class="label">总学分：</text>
      <text class="value">{{detail.credits}} 分</text>
    </view>
    <view class="info-row" wx:if="{{detail.achievedHours}}">
      <text class="label">已获学时：</text>
      <text class="value">{{detail.achievedHours}} 小时</text>
    </view>
    <view class="info-row" wx:if="{{detail.earnedCredits}}">
      <text class="label">已获学分：</text>
      <text class="value">{{detail.earnedCredits}} 分</text>
    </view>
    <view class="info-row" wx:if="{{detail.remark}}">
      <text class="label">备注：</text>
      <text class="value">{{detail.remark}}</text>
    </view>
    <view class="info-row" wx:if="{{detail.description}}">
      <text class="label">班次描述：</text>
      <text class="value">{{detail.description}}</text>
    </view>
  </view>

  <view class="footer-btns">
    <button class="btn-primary" bindtap="goBack">返回</button>
  </view>
</view> 
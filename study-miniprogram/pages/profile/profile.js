const app = getApp()

Page({
  data: {
    userInfo: {},
    stats: {}
  },

  onLoad() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  onShow() {
    this.loadUserStats()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      // 处理头像URL
      if (userInfo.avatar && !userInfo.avatar.startsWith('http')) {
        userInfo.avatar = app.globalData.baseUrl + userInfo.avatar
      }
      this.setData({ userInfo })
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        // 处理头像URL
        if (userInfo.avatar && !userInfo.avatar.startsWith('http')) {
          userInfo.avatar = app.globalData.baseUrl + userInfo.avatar
        }
        this.setData({ userInfo })
      }

      const res = await app.request({
        url: '/study/statistics/sumHours',
        data: {
          phonenumber: userInfo.phonenumber,
        }
      })
      // if (res.data) {
      this.setData({
        stats: res.rows[0]
      })
      // }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 查看考勤记录
  viewAttendanceHistory() {
    wx.navigateTo({
      url: '/pages/attendance-history/attendance-history'
    })
  },

  // 查看请假记录
  viewLeaveHistory() {
    wx.navigateTo({
      url: '/pages/leave-history/leave-history'
    })
  },

  // 查看学习证书
  viewCertificates() {
    wx.showModal({
      title: '提示',
      content: '学习证书功能正在开发中',
      showCancel: false
    })
  },

  // 设置
  viewSettings() {
    wx.showModal({
      title: '提示',
      content: '设置功能正在开发中',
      showCancel: false
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
        }
      }
    })
  }
}) 
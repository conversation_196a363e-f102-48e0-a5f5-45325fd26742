const app = getApp()

Page({
  data: {
    activeTab: 'substantive',
    rankingData: [],
    myRanking: {},
    loading: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 20
  },

  onLoad() {
    this.loadRankingData()
    this.loadMyRanking()
  },

  // 切换排行榜类型
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    if (tab === this.data.activeTab) return

    this.setData({
      activeTab: tab,
      rankingData: [],
      currentPage: 1,
      hasMore: true
    })

    this.loadRankingData()
    this.loadMyRanking()
  },

  // 加载排行榜数据
  async loadRankingData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const res = await app.request({
        url: '/study/ranking/list',
        data: {
          cadreType: this.data.activeTab,
          pageNum: this.data.currentPage,
          pageSize: this.data.pageSize
        }
      })

      const newData = (res.rows || []).map(item => {
        // 处理头像URL
        if (item.avatar && !item.avatar.startsWith('http')) {
          item.avatar = app.globalData.baseUrl + item.avatar
        }
        return item
      })
      
      const rankingData = this.data.currentPage === 1 ? newData : [...this.data.rankingData, ...newData]

      this.setData({
        rankingData,
        hasMore: newData.length === this.data.pageSize
      })

    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载我的排名
  async loadMyRanking() {
    try {
      const res = await app.request({
        url: '/study/ranking/my',
        data: {
          cadreType: this.data.activeTab
        }
      })

      this.setData({
        myRanking: res.data || {}
      })

    } catch (error) {
      console.error('加载我的排名失败:', error)
    }
  },

  // 加载更多
  loadMore() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({
      currentPage: this.data.currentPage + 1
    })

    this.loadRankingData()
  },

  // 获取奖牌样式
  getMedalClass(rank) {
    switch (rank) {
      case 1:
        return 'gold'
      case 2:
        return 'silver'
      case 3:
        return 'bronze'
      default:
        return ''
    }
  }
}) 
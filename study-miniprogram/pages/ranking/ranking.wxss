/* pages/ranking/ranking.wxss */

.ranking-tabs {
  display: flex;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 15rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #409EFF;
  color: white;
  font-weight: 500;
}

.my-ranking {
  margin-bottom: 30rpx;
}

.my-rank-info {
  display: flex;
  justify-content: space-around;
}

.rank-item {
  text-align: center;
}

.rank-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.rank-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #409EFF;
}

.ranking-list {
  margin-bottom: 30rpx;
}

.ranking-item {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx 16rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  justify-content: flex-start;
  gap: 10rpx;
}

.rank-number {
  width: 44rpx;
  text-align: center;
  margin-right: 8rpx;
}

.medal {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24rpx;
}

.medal.gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.medal.silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}

.medal.bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.rank-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-avatar {
  width: 44rpx;
  height: 44rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
  object-fit: cover;
}

.user-info {
  flex: 2;
  margin-right: 8rpx;
  min-width: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-dept {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.user-type {
  font-size: 24rpx;
  color: #999;
}

.score-info {
  text-align: right;
}

.total-score {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8rpx;
}
.total-completedScore {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8rpx;
}

.last-study {
  font-size: 32rpx;
  color: #999;

}

.load-more {
  text-align: center;
  padding: 30rpx;
  color: #666;
  font-size: 28rpx;
}

.score-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
}

.score-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  text-align: center;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #409EFF;
  text-align: center;
}
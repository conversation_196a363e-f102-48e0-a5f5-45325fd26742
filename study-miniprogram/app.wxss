/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  padding: 20rpx;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #409EFF;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  border: none;
  font-size: 32rpx;
}

.btn-primary:hover {
  background-color: #337ecc;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #409EFF;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.card-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 列表样式 */
.list-item {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 2rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:first-child {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.list-item:last-child {
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 状态标签 */
.status-tag {
  min-width: 80rpx;
  height: 48rpx;
  padding: 0 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  box-sizing: border-box;
  font-weight: 500;
  white-space: nowrap;
}

.status-success {
  background-color: #67C23A;
}

.status-warning {
  background-color: #E6A23C;
}

.status-danger {
  background-color: #F56C6C;
}

.status-info {
  background-color: #909399;
}

/* 间距工具类 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.p-10 { padding: 20rpx; }
.p-20 { padding: 40rpx; }

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Flex布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 文本样式 */
.text-primary {
  color: #409EFF;
}

.text-success {
  color: #67C23A;
}

.text-warning {
  color: #E6A23C;
}

.text-danger {
  color: #F56C6C;
}

.text-muted {
  color: #999;
}

/* 弹窗内容强制换行 */
.weui-dialog__bd, .modal-content, .wx-modal-content {
  white-space: pre-line !important;
  word-break: break-all !important;
} 
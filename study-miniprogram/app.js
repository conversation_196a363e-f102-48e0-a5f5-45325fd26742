// app.js
App({
  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:8080', // 后端API地址，部署时需要修改为实际地址
    token: ''
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('小程序启动')
    
    // 检查登录状态
    this.checkLogin()
  },

  // 检查登录状态
  checkLogin() {
    const token = wx.getStorageSync('token')
    if (token) {
      this.globalData.token = token
      // 可以在这里验证token是否有效
    }
  },

  // 登录方法
  login(phone, password) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}/miniprogram/login`,
        method: 'POST',
        data: {
          username: phone,
          password: password,
          loginType: 'phone' // 使用手机号登录
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.globalData.token = res.data.token
            wx.setStorageSync('token', res.data.token)
            
            // 处理用户头像URL
            const userInfo = res.data.user
            if (userInfo.avatar && !userInfo.avatar.startsWith('http')) {
              userInfo.avatar = this.globalData.baseUrl + userInfo.avatar
            }
            
            wx.setStorageSync('userInfo', userInfo)
            resolve(res.data)
          } else {
            reject(res.data.msg || '登录失败')
          }
        },
        fail: (error) => {
          reject('网络错误')
        }
      })
    })
  },

  // 退出登录
  logout() {
    this.globalData.token = ''
    this.globalData.userInfo = null
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.globalData.token}`,
          ...options.header
        },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data)
          } else if (res.data.code === 401) {
            // token过期，重新登录
            this.logout()
            reject('登录已过期')
          } else {
            reject(res.data.msg || '请求失败')
          }
        },
        fail: (error) => {
          reject('网络错误')
        }
      })
    })
  }
}) 
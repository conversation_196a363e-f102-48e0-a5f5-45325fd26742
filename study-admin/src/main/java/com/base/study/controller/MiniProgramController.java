package com.base.study.controller;

import java.math.RoundingMode;
import java.util.*;
import java.math.BigDecimal;

import com.base.system.domain.SysNotice;
import com.base.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.DateUtils;
import com.base.study.domain.*;
import com.base.study.domain.vo.StudyHourRankingVo;
import com.base.study.service.*;
import com.base.system.service.ISysUserService;
import com.base.common.core.domain.entity.SysUser;

/**
 * 小程序专用接口Controller
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/study")
public class MiniProgramController extends BaseController
{
    @Autowired
    private IStudyAttendanceService studyAttendanceService;
    
    @Autowired
    private IStudyCourseService studyCourseService;
    
    @Autowired
    private IStudyLeaveRequestService studyLeaveRequestService;
    
    @Autowired
    private IStudyStatisticsService studyStatisticsService;
    
    @Autowired
    private ISysUserService userService;

    @Autowired
    private IStudyHourTargetService studyHourTargetService;

    @Autowired
    private IStudyRegistrationService studyRegistrationService;

    @Autowired
    private ISysNoticeService noticeService;

    /**
     * 获取今日考勤状态
     */
    @GetMapping("/attendance/today")
    public AjaxResult getTodayAttendance(@RequestParam String date)
    {
        try {
            Long userId = getUserId();
            StudyAttendance queryParam = new StudyAttendance();
            queryParam.setUserId(userId);
            
            // 查询今日考勤记录
            List<StudyAttendance> attendanceList = studyAttendanceService.selectStudyAttendanceList(queryParam);
            
            // 筛选今日记录
            Date targetDate = DateUtils.parseDate(date);
            StudyAttendance todayAttendance = null;
            
            for (StudyAttendance attendance : attendanceList) {
                if (attendance.getCheckInTime() != null && 
                    DateUtils.isSameDay(attendance.getCheckInTime(), targetDate)) {
                    todayAttendance = attendance;
                    break;
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            if (todayAttendance != null) {
                result.put("checkInTime", todayAttendance.getCheckInTime());
                result.put("checkOutTime", todayAttendance.getCheckOutTime());
                result.put("attendanceStatus", todayAttendance.getAttendanceStatus());
            }
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取今日考勤状态失败");
        }
    }

    /**
     * 签到
     */
    @PostMapping("/attendance/checkin")
    public AjaxResult checkIn(@RequestBody Map<String, Object> params)
    {
        try {
            Long userId = getUserId();
            SysUser user = userService.selectUserById(userId);
            
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }
            
            // 调用现有的打卡接口
            return studyAttendanceService.punch(user.getPhonenumber(), new Date());
        } catch (Exception e) {
            return AjaxResult.error("签到失败：" + e.getMessage());
        }
    }

    /**
     * 签退
     */
    @PostMapping("/attendance/checkout")
    public AjaxResult checkOut()
    {
        try {
            Long userId = getUserId();
            SysUser user = userService.selectUserById(userId);
            
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }
            
            // 调用现有的打卡接口（第二次调用为签退）
            return studyAttendanceService.punch(user.getPhonenumber(), new Date());
        } catch (Exception e) {
            return AjaxResult.error("签退失败：" + e.getMessage());
        }
    }

    /**
     * 获取考勤记录列表
     */
    @GetMapping("/attendance/records")
    public TableDataInfo getAttendanceList(@RequestParam(defaultValue = "1") Integer pageNum,
                                          @RequestParam(defaultValue = "10") Integer pageSize)
    {
        startPage();
        StudyAttendance queryParam = new StudyAttendance();
        queryParam.setUserId(getUserId());
        List<StudyAttendance> list = studyAttendanceService.selectStudyAttendanceList(queryParam);
        return getDataTable(list);
    }

    /**
     * 获取当前课程
     */
    @GetMapping("/course/current")
    public AjaxResult getCurrentCourse()
    {
        try {
            Long userId = getUserId();
            Date now = new Date();
            
            // 查询当前时间段的课程
            StudyCourse queryParam = new StudyCourse();
            List<StudyCourse> courseList = studyCourseService.selectStudyCourseList(queryParam);
            
            // 简化处理：返回最近的课程
            StudyCourse currentCourse = null;
            if (!courseList.isEmpty()) {
                currentCourse = courseList.get(0);
            }
            
            return AjaxResult.success(currentCourse);
        } catch (Exception e) {
            return AjaxResult.error("获取当前课程失败");
        }
    }

    /**
     * 获取排行榜列表
     */
    @GetMapping("/ranking/list")
    public TableDataInfo getRankingList(@RequestParam String cadreType,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "20") Integer pageSize)
    {
        startPage();
        
        // 转换cadreType
        Integer cadreTypeInt = convertCadreType(cadreType);
        Integer year = java.time.Year.now().getValue();
        
        List<StudyHourRankingVo> list = studyStatisticsService.selectStudyHourRanking(cadreTypeInt, year, null);
        
        // 为小程序添加排名字段和格式化数据
        for (int i = 0; i < list.size(); i++) {
            StudyHourRankingVo ranking = list.get(i);
            // 设置totalHours字段（小程序需要）
            if (ranking.getTotalHours() == null && ranking.getCompletedHours() != null) {
                ranking.setTotalHours(ranking.getCompletedHours());
            }
            // 设置职级名称
            ranking.setCadreTypeName(getCadreTypeName(cadreTypeInt));
        }
        
        return getDataTable(list);
    }

    /**
     * 获取我的排名
     */
    @GetMapping("/ranking/my")
    public AjaxResult getMyRanking(@RequestParam String cadreType)
    {
        try {
            Long userId = getUserId();
            Integer cadreTypeInt = convertCadreType(cadreType);
            Integer year = java.time.Year.now().getValue();
            
            List<StudyHourRankingVo> rankingList = studyStatisticsService.selectStudyHourRanking(cadreTypeInt, year, null);
            
            // 查找当前用户的排名
            Map<String, Object> myRanking = new HashMap<>();
            for (int i = 0; i < rankingList.size(); i++) {
                StudyHourRankingVo ranking = rankingList.get(i);
                if (ranking.getUserId().equals(userId)) {
                    myRanking.put("rank", i + 1);
                    myRanking.put("totalHours", ranking.getTotalHours());
                    myRanking.put("userName", ranking.getUserName());
                    myRanking.put("score", ranking.getScore());
                    break;
                }
            }
            
            return AjaxResult.success(myRanking);
        } catch (Exception e) {
            return AjaxResult.error("获取我的排名失败");
        }
    }

    /**
     * 申请请假
     */
    @PostMapping("/leave/apply")
    public AjaxResult applyLeave(@RequestBody StudyLeaveRequest leaveRequest)
    {
        try {
            leaveRequest.setUserId(getUserId());
            leaveRequest.setCreateBy(getUsername());
            leaveRequest.setCreateTime(new Date());
            leaveRequest.setAuditStatus("0"); // 待审批
            
            int result = studyLeaveRequestService.insertStudyLeaveRequest(leaveRequest);
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("申请请假失败：" + e.getMessage());
        }
    }

    /**
     * 获取请假记录列表
     */
    @GetMapping("/leave/list")
    public TableDataInfo getLeaveList(@RequestParam(defaultValue = "1") Integer pageNum,
                                     @RequestParam(defaultValue = "20") Integer pageSize)
    {
        startPage();
        StudyLeaveRequest queryParam = new StudyLeaveRequest();
        queryParam.setUserId(getUserId());
        List<StudyLeaveRequest> list = studyLeaveRequestService.selectStudyLeaveRequestList(queryParam);
        return getDataTable(list);
    }

    /**
     * 获取个人统计数据
     */
    @GetMapping("/statistics/summary")
    public AjaxResult getPersonalStatistics()
    {
        try {
            Long userId = getUserId();
            
            // 获取个人考勤统计
            StudyAttendance queryParam = new StudyAttendance();
            queryParam.setUserId(userId);
            List<StudyAttendance> attendanceList = studyAttendanceService.selectStudyAttendanceList(queryParam);
            
            // 计算统计数据
            BigDecimal totalHours = BigDecimal.ZERO;
            BigDecimal totalCredits = BigDecimal.ZERO;
            int totalAttendance = attendanceList.size();
            int normalCount = 0;
            
            for (StudyAttendance attendance : attendanceList) {
                if (attendance.getCalculatedHours() != null) {
                    totalHours = totalHours.add(attendance.getCalculatedHours());
                }
                if (attendance.getCalculatedCredits() != null) {
                    totalCredits = totalCredits.add(attendance.getCalculatedCredits());
                }
                if ("正常".equals(attendance.getAttendanceStatus())) {
                    normalCount++;
                }
            }
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalHours", totalHours);
            statistics.put("totalCredits", totalCredits);
            statistics.put("totalAttendance", totalAttendance);
            statistics.put("normalRate", totalAttendance > 0 ? 
                (normalCount * 100.0 / totalAttendance) : 0);
            statistics.put("completionRate", 80.0); // 假设完成率，可根据实际需求计算
            
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            return AjaxResult.error("获取个人统计失败");
        }
    }

    /**
     * 转换职级类型
     */
    private Integer convertCadreType(String cadreType) {
        switch (cadreType) {
            case "substantive":
                return 0; // 实职领导
            case "section":
                return 1; // 科级干部
            case "ordinary":
                return 2; // 普通党员
            case "other":
                return 3; // 其他人员
            default:
                return 2; // 默认普通党员
        }
    }
    
    /**
     * 获取职级名称
     */
    private String getCadreTypeName(Integer cadreType) {
        switch (cadreType) {
            case 0:
                return "实职领导";
            case 1:
                return "科级干部";
            case 2:
                return "普通党员";
            case 3:
                return "其他人员";
            default:
                return "普通党员";
        }
    }

    /**
     * 获取登录人员的累计学时与年度完成更率
     * @return
     */
    @GetMapping("/statistics/sumHours")
    public TableDataInfo getSumHoursList(@RequestParam(value = "phonenumber",required = false) String phonenumber)
    {
        //需要完成的年度学时指标
        BigDecimal hour = new BigDecimal(0);

        //获取年度学时指标
        StudyHourTarget studyHourTarget = new StudyHourTarget();
        //当前年份
        studyHourTarget.setYear(DateUtils.getCurrentYear());
        //当前年份
        List<StudyHourTarget> studyHourTargets = studyHourTargetService.selectStudyHourTargetList(studyHourTarget);

        //根据登录人，获取干部类型，用来判断对应的指标
        SysUser sysUser = userService.selectUserByPhonenumber(phonenumber);
        Integer cadreTypeByUser = Integer.valueOf(sysUser.getCadreType());
        for (StudyHourTarget studyHourTargetByDb:studyHourTargets) {
            if(studyHourTargetByDb.getCadreType() == cadreTypeByUser){
                //获取学时指标
                hour = hour.add(BigDecimal.valueOf(studyHourTargetByDb.getHour()));
            }
        }

        //获取累计完成学时
        BigDecimal sumHours = studyRegistrationService.selectSumHoursByUserId(sysUser.getUserId());

        //计算完成率=已完成/指标*100
        BigDecimal completionRate = sumHours.divide(hour, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

        //获取排名信息
        List<StudyHourRankingVo> rankingList = studyStatisticsService.selectStudyHourRanking(cadreTypeByUser, DateUtils.getCurrentYear(), null);
        // 查找当前用户的排名
        Integer rank = 0;
        for (int i = 0; i < rankingList.size(); i++) {
            StudyHourRankingVo ranking = rankingList.get(i);
            if (ranking.getUserId().equals(sysUser.getUserId())) {
                rank = i + 1;
                break;
            }
        }

        //拼装返回数据
        StudyHourRankingVo studyHourRankingVo = new StudyHourRankingVo();
        studyHourRankingVo.setTotalHours(sumHours);
        studyHourRankingVo.setCompletionRate(completionRate);
        studyHourRankingVo.setRanking(rank);
        List<StudyHourRankingVo> list = new ArrayList<>();
        list.add(studyHourRankingVo);
        return getDataTable(list);
    }

    /**
     * 获取通知信息
     * @param notice
     * @return
     */
    @GetMapping("/notice/list")
    public TableDataInfo list(SysNotice notice)
    {
        startPage();
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 小程序报名接口
     */
    @PostMapping("/registration/add")
    public AjaxResult miniProgramAddRegistration(@RequestBody Map<String, Object> param) {
        Long classId = param.get("classId") != null ? Long.valueOf(param.get("classId").toString()) : null;
        Long userId = param.get("userId") != null ? Long.valueOf(param.get("userId").toString()) : null;
        String status = param.get("status") != null ? param.get("status").toString() : "0";
        if (classId == null || userId == null) {
            return AjaxResult.error("参数错误");
        }
        // 查询用户手机号
        SysUser user = userService.selectUserById(userId);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }
        StudyRegistration registration = new StudyRegistration();
        registration.setClassId(classId);
        registration.setUserId(userId);
        registration.setStatus(status);
        registration.setPhonenumber(user.getPhonenumber());
        return studyRegistrationService.insertStudyRegistration(registration);
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.study.mapper.StudyHourTargetMapper">

    <resultMap type="com.base.study.domain.StudyHourTarget" id="StudyHourTargetResult">
        <result property="id"    column="id"    />
        <result property="year"    column="year"    />
        <result property="cadreType"    column="cadre_type"    />
        <result property="category"    column="category"    />
        <result property="credit"    column="credit"    />
        <result property="hour"    column="hour"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectStudyHourTargetVo">
        select id, year, cadre_type, category, credit, hour, create_by, create_time, update_by, update_time, remark from study_hour_target
    </sql>

    <select id="selectStudyHourTargetList" parameterType="com.base.study.domain.StudyHourTarget" resultMap="StudyHourTargetResult">
        <include refid="selectStudyHourTargetVo"/>
        <where>
            <if test="year != null "> and year = #{year}</if>
            <if test="cadreType != null "> and cadre_type = #{cadreType}</if>
        </where>
    </select>

    <select id="selectStudyHourTargetById" parameterType="Long" resultMap="StudyHourTargetResult">
        <include refid="selectStudyHourTargetVo"/>
        where id = #{id}
    </select>

    <insert id="insertStudyHourTarget" parameterType="com.base.study.domain.StudyHourTarget" useGeneratedKeys="true" keyProperty="id">
        insert into study_hour_target
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="year != null">year,</if>
            <if test="cadreType != null">cadre_type,</if>
            <if test="category != null">category,</if>
            <if test="credit != null">credit,</if>
            <if test="hour != null">hour,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="year != null">#{year},</if>
            <if test="cadreType != null">#{cadreType},</if>
            <if test="category != null">#{category},</if>
            <if test="credit != null">#{credit},</if>
            <if test="hour != null">#{hour},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateStudyHourTarget" parameterType="com.base.study.domain.StudyHourTarget">
        update study_hour_target
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="cadreType != null">cadre_type = #{cadreType},</if>
            <if test="category != null">category = #{category},</if>
            <if test="credit != null">credit = #{credit},</if>
            <if test="hour != null">hour = #{hour},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudyHourTargetById" parameterType="Long">
        delete from study_hour_target where id = #{id}
    </delete>

    <delete id="deleteStudyHourTargetByIds" parameterType="String">
        delete from study_hour_target where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 
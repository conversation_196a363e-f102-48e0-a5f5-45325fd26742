# 考勤机手动同步功能说明

## 功能概述
为满足自动同步异常或测试场景的需求，系统增加了完整的手动同步功能，支持在多个页面进行人员信息的手动同步和删除操作。

## 功能分布

### 1. 设备管理页面 (`/study/device`)
**位置**：考勤设备列表 → 操作下拉菜单
**功能**：
- **同步所有用户**：将系统中所有启用用户同步到指定设备
- **清除所有用户**：清除指定设备中的所有用户信息
- **同步考勤记录**：从设备同步最近24小时的考勤记录

**使用场景**：
- 设备初始化或重置后的用户批量导入
- 设备故障恢复后的数据同步
- 测试环境的数据清理

### 2. 班级管理页面 (`/study/class`)
**位置**：班级列表 → 考勤设备操作菜单（仅显示配置了device_ids的班级）
**功能**：
- **同步学员到设备**：将班级所有学员同步到关联的考勤设备
- **从设备删除学员**：从关联设备删除班级所有学员

**使用场景**：
- 课程开始前的紧急学员授权
- 自动同步失败后的手动补救
- 特殊情况下的临时授权

## API接口

### 设备管理相关
```http
POST /study/device/syncAllUsers/{deviceId}      # 同步所有用户到设备
POST /study/device/clearAllUsers/{deviceId}     # 清除设备所有用户
POST /study/device/syncUser/{deviceId}/{userId} # 同步单个用户到设备
POST /study/device/removeUser/{deviceId}/{userId} # 从设备删除单个用户
```

### 班级管理相关
```http
POST /study/device/syncUsers/{classId}          # 同步班级学员到设备
POST /study/device/removeUsers/{classId}        # 从设备删除班级学员
```

## 权限配置

### 设备管理权限
- `study:device:syncUsers` - 用户同步权限
- `study:device:removeUsers` - 用户删除权限
- `study:device:syncAttendance` - 考勤同步权限

## 操作流程

### 1. 批量同步操作流程
1. **选择目标**：在相应页面选择设备或用户
2. **确认操作**：系统弹出确认对话框，说明操作影响
3. **执行同步**：显示进度提示，执行同步操作
4. **结果反馈**：显示同步结果，包括成功/失败数量

### 2. 错误处理机制
- **连接失败**：跳过当前设备，继续同步其他设备
- **用户不存在**：记录错误日志，不影响其他用户同步
- **设备不支持**：自动降级到基础功能
- **超时处理**：自动重试机制，超时后记录失败

## 监控和日志

### 操作日志
所有手动同步操作都会记录到系统日志，包括：
- 操作人员、时间、类型
- 目标设备和用户信息
- 操作结果和错误信息

### 性能监控
- 同步操作响应时间
- 成功率统计
- 设备连接状态监控

## 注意事项

### 1. 操作限制
- **超级管理员用户**（userId=1）不会显示设备同步选项
- **禁用用户**不会被同步到设备
- **禁用设备**不参与同步操作

### 2. 性能考虑
- **批量操作**：大量用户同步可能需要较长时间，请耐心等待
- **并发限制**：避免同时执行多个同步操作
- **网络要求**：确保服务器与考勤设备网络连通

### 3. 数据安全
- **删除操作不可恢复**：请谨慎执行用户删除操作
- **备份建议**：重要操作前建议备份设备配置
- **权限控制**：严格控制同步操作权限

## 故障排除

### 常见问题
1. **同步失败**：检查设备网络连接和认证信息
2. **部分成功**：查看详细日志，重新同步失败的部分
3. **用户未显示**：确认用户状态为启用，设备布防已开启
4. **权限错误**：联系管理员检查操作权限配置

### 日志查看
```bash
# 查看同步操作日志
tail -f /path/to/logs/study-hour-management.log | grep -E "同步|sync"

# 查看设备通信日志
tail -f /path/to/logs/study-hour-management.log | grep -E "设备|Device"
```

## 更新历史
- **v1.0**：基础手动同步功能
- **v1.1**：增加批量操作支持
- **v1.2**：完善错误处理和重试机制
- **v1.3**：优化功能分布，聚焦设备和班级管理 
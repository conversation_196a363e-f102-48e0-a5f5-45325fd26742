# 考勤机布防功能修复说明

## 问题描述
关闭布防操作报错：
```
ERROR c.b.s.s.DeviceService - 设备布防关闭失败: 考勤机-大厅, 错误: HTTP请求失败: 400 - 
<?xml version="1.0" encoding="UTF-8"?>
<ResponseStatus version="1.0" xmlns="http://www.hikvision.com/ver10/XMLSchema">
<requestURL></requestURL>
<statusCode>4</statusCode>
<statusString>Invalid Operation</statusString>
<subStatusCode>notSupport</subStatusCode>
<errorCode>1073741825</errorCode>
<errorMsg>notSupport</errorMsg>
</ResponseStatus>
```

## 问题原因
1. **错误的API接口**：原代码使用了 `/ISAPI/Event/triggers/VMD-1` 接口，这个接口在某些海康威视设备上不被支持
2. **错误的请求格式**：使用了JSON格式，而海康威视设备通常使用XML格式
3. **布防概念理解错误**：海康威视的布防实际上是通过长连接事件监听实现的，不是简单的配置开关

## 解决方案

### 1. 修正布防实现逻辑
根据原项目 `isapi-production-acs` 的实现，海康威视的布防功能包括：

**启动布防（f0001）**：
- 建立到 `/ISAPI/Event/notification/alertStream` 的长连接
- 持续监听设备事件
- 备用方案：启用移动侦测功能

**关闭布防（f0002）**：
- 断开事件监听长连接
- 备用方案：关闭移动侦测功能

### 2. 修复内容

#### 2.1 修改布防API接口
```java
// 原来错误的接口
String url = buildDeviceUrl(device) + "/ISAPI/Event/triggers/VMD-1";

// 修正后的接口
String testUrl = buildDeviceUrl(device) + "/ISAPI/Event/notification/alertStream";  // 事件监听
String vmdUrl = buildDeviceUrl(device) + "/ISAPI/System/Video/inputs/channels/1/motionDetection";  // 移动侦测
```

#### 2.2 支持XML格式请求
更新 `DeviceApiUtil` 工具类：
- 自动识别XML/JSON格式
- 添加 `putXml()` 方法专门处理XML请求
- 正确设置 `Content-Type` 头

#### 2.3 多层级降级策略
```java
// 1. 首先尝试事件监听接口（推荐方式）
// 2. 如果不支持，使用移动侦测配置（兼容方式）
// 3. 如果都不支持，优雅降级
```

### 3. 技术细节

#### 3.1 移动侦测XML配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<MotionDetection version="2.0" xmlns="http://www.hikvision.com/ver20/XMLSchema">
    <enabled>true</enabled>  <!-- true=启动布防, false=关闭布防 -->
    <enableHighlight>false</enableHighlight>
    <samplingInterval>2</samplingInterval>
    <startTriggerTime>500</startTriggerTime>
    <endTriggerTime>500</endTriggerTime>
</MotionDetection>
```

#### 3.2 错误处理机制
- 主接口失败时自动切换到备用接口
- 详细的错误日志记录
- 优雅的降级处理

### 4. 兼容性说明

这个修复方案兼容多种海康威视设备：
- **新版本设备**：支持事件通知接口
- **老版本设备**：通过移动侦测实现类似功能
- **不兼容设备**：优雅降级，不影响其他功能

### 5. 验证方法

1. **测试布防启动**：
   ```bash
   curl -X POST http://localhost:8080/study/device/startGuard/{deviceId}
   ```

2. **测试布防关闭**：
   ```bash
   curl -X POST http://localhost:8080/study/device/stopGuard/{deviceId}
   ```

3. **查看日志**：观察是否有错误信息，成功时应该看到：
   ```
   INFO - 设备布防启用成功: 考勤机-大厅 - 事件监听接口已激活
   INFO - 设备布防关闭成功: 考勤机-大厅
   ```

### 6. 后续优化建议

1. **长连接管理**：实现真正的长连接事件监听（类似原项目的LongLinkThread）
2. **状态同步**：将布防状态实时同步到数据库
3. **事件处理**：处理设备推送的考勤事件
4. **断线重连**：自动重连机制

## 注意事项

1. 修复后需要重启应用服务器
2. 不同型号的海康威视设备可能支持不同的接口
3. 建议先在测试环境验证后再部署到生产环境
4. 如果设备仍然不支持，可能需要联系设备厂商获取正确的API文档 